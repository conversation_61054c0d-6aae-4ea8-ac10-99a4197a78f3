{"name": "trang-backend", "version": "0.1.0", "private": true, "description": "A Strapi application", "scripts": {"build": "strapi build", "console": "strapi console", "deploy": "strapi deploy", "dev": "strapi develop", "develop": "strapi develop", "seed:example": "node ./scripts/seed.js", "start": "strapi start", "strapi": "strapi", "upgrade": "npx @strapi/upgrade latest", "upgrade:dry": "npx @strapi/upgrade latest --dry", "backup": "node ./scripts/create-schema-backup.js", "backup-full": "node ./scripts/create-full-backup.js"}, "dependencies": {"@_sh/strapi-plugin-ckeditor": "^6.0.1", "@strapi/plugin-cloud": "5.13.0", "@strapi/plugin-documentation": "^5.13.0", "@strapi/plugin-seo": "^2.0.8", "@strapi/plugin-users-permissions": "5.13.0", "@strapi/strapi": "5.13.0", "fs-extra": "^10.0.0", "koa2-ratelimit": "^1.1.3", "mime-types": "^2.1.27", "mysql2": "3.9.8", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "strapi-custom-action-perform-page": "^0.0.13", "strapi-plugin-multi-select": "^2.1.1", "styled-components": "^6.0.0", "tree-menus": "^1.0.4"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "typescript": "^5"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************", "installId": "4f51b941fa3145dcf3d0b6dae51eaa1bad8ebdf0d1de714cfc84d448e96383f3"}}