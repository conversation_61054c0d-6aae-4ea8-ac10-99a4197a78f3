{"kind": "collectionType", "collectionName": "about_uses", "info": {"singularName": "about-us", "pluralName": "about-uses", "displayName": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"title": {"type": "string", "required": true, "pluginOptions": {"i18n": {"localized": true}}}, "slug": {"type": "uid", "targetField": "title", "required": true}, "content": {"type": "customField", "options": {"preset": "defaultHtml"}, "customField": "plugin::ckeditor5.CKEditor"}, "dynamic_content": {"type": "dynamiczone", "components": ["shared.slider", "shared.rich-text", "shared.media"], "pluginOptions": {"i18n": {"localized": false}}}, "order": {"type": "integer", "default": 1}}}