# Sử dụng Node.js 20.9.0 Alpine để giảm kích thước image
FROM node:20.9.0-alpine

# Cài đặt các dependencies cần thiết cho Alpine
RUN apk add --no-cache \
    libc6-compat \
    python3 \
    make \
    g++ \
    vips-dev

# Tạo thư mục làm việc
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY yarn.lock ./

# Cài đặt dependencies
RUN yarn install --frozen-lockfile

# Copy source code
COPY . .

# Build ứng dụng
RUN yarn build

# Tạo user non-root để chạy ứng dụng
RUN addgroup -g 1001 -S nodejs
RUN adduser -S strapi -u 1001

# Chuyển ownership của thư mục app cho user strapi
RUN chown -R strapi:nodejs /app
USER strapi

# Expose port
EXPOSE 1337

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node healthcheck.js

# Start command
CMD ["yarn", "start"]
