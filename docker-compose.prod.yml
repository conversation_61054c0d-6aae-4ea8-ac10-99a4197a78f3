version: '3.8'

services:
  # Strapi Application (Production)
  strapi:
    build: 
      context: .
      dockerfile: Dockerfile
    container_name: trang-backend-strapi-prod
    restart: unless-stopped
    env_file: .env.production
    environment:
      DATABASE_CLIENT: mysql
      DATABASE_HOST: mysql
      DATABASE_PORT: 3306
      DATABASE_NAME: ${DATABASE_NAME:-trang_db}
      DATABASE_USERNAME: ${DATABASE_USERNAME:-strapi}
      DATABASE_PASSWORD: ${DATABASE_PASSWORD:-strapi}
      NODE_ENV: production
    volumes:
      - strapi_uploads:/app/public/uploads
    ports:
      - "${PORT:-1337}:1337"
    networks:
      - strapi-network
    depends_on:
      mysql:
        condition: service_healthy
    command: ["yarn", "start"]

  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: trang-backend-mysql-prod
    restart: unless-stopped
    env_file: .env.production
    environment:
      MYSQL_DATABASE: ${DATABASE_NAME:-trang_db}
      MYSQL_USER: ${DATABASE_USERNAME:-strapi}
      MYSQL_PASSWORD: ${DATABASE_PASSWORD:-strapi}
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-root}
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - strapi-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Nginx Reverse Proxy (Production)
  nginx:
    image: nginx:alpine
    container_name: trang-backend-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    networks:
      - strapi-network
    depends_on:
      - strapi

volumes:
  mysql_data:
    driver: local
  strapi_uploads:
    driver: local

networks:
  strapi-network:
    driver: bridge
