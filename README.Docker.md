# Docker Setup cho Trang Backend

Hướng dẫn chạy dự án Strapi với Docker Compose sử dụng Node.js 20.9.0.

## Y<PERSON><PERSON> cầu hệ thống

- Docker Engine 20.10+
- Docker Compose 2.0+
- Ít nhất 2GB RAM
- Ít nhất 5GB dung lượng ổ cứng

## Cấu trúc Docker

### Services được cung cấp:

1. **strapi** - Ứng dụng Strapi chính (Node.js 20.9.0)
2. **mysql** - Database MySQL 8.0
3. **phpmyadmin** - Giao diện quản lý database (tùy chọn)

## Cách sử dụng

### 1. Development Mode

```bash
# Clone repository và di chuyển vào thư mục
cd trang_backend

# Khởi động tất cả services
docker-compose up -d

# Xem logs
docker-compose logs -f strapi

# Dừng services
docker-compose down
```

### 2. Production Mode

```bash
# Sử dụng file docker-compose.prod.yml
docker-compose -f docker-compose.prod.yml up -d
```

### 3. Chỉ khởi động một số services

```bash
# Chỉ khởi động Strapi và MySQL
docker-compose up -d strapi mysql

# Chỉ khởi động MySQL
docker-compose up -d mysql
```

## Cấu hình

### Biến môi trường (.env)

Các biến môi trường quan trọng:

```env
# Server
HOST=0.0.0.0
PORT=1337
NODE_ENV=development

# Database
DATABASE_CLIENT=mysql
DATABASE_HOST=mysql
DATABASE_NAME=trang_db
DATABASE_USERNAME=strapi
DATABASE_PASSWORD=strapi
MYSQL_ROOT_PASSWORD=root

# Strapi Secrets
APP_KEYS="key1,key2,key3,key4"
API_TOKEN_SALT=your_salt
ADMIN_JWT_SECRET=your_secret
TRANSFER_TOKEN_SALT=your_salt
JWT_SECRET=your_jwt_secret
```

### Ports

- **1337** - Strapi application
- **3306** - MySQL database
- **8080** - phpMyAdmin (tùy chọn)

## Quản lý dữ liệu

### Volumes

- `mysql_data` - Dữ liệu MySQL
- `strapi_uploads` - Files upload của Strapi
- `strapi_node_modules` - Node modules (development)

### Backup Database

```bash
# Backup
docker-compose exec mysql mysqldump -u root -p trang_db > backup.sql

# Restore
docker-compose exec -T mysql mysql -u root -p trang_db < backup.sql
```

## Troubleshooting

### 1. Port đã được sử dụng

```bash
# Kiểm tra port đang sử dụng
lsof -i :1337
lsof -i :3306

# Thay đổi port trong .env
PORT=1338
DATABASE_PORT=3307
```

### 2. Database connection error

```bash
# Kiểm tra MySQL container
docker-compose logs mysql

# Restart MySQL
docker-compose restart mysql
```

### 3. Strapi không khởi động

```bash
# Xem logs chi tiết
docker-compose logs strapi

# Rebuild container
docker-compose build --no-cache strapi
docker-compose up -d strapi
```

### 4. Xóa tất cả dữ liệu và bắt đầu lại

```bash
# Dừng và xóa containers, volumes
docker-compose down -v

# Xóa images (tùy chọn)
docker-compose down --rmi all

# Khởi động lại
docker-compose up -d
```

## Development Tips

### 1. Hot reload

Development mode đã được cấu hình với volume mounting để hỗ trợ hot reload.

### 2. Truy cập container

```bash
# Truy cập Strapi container
docker-compose exec strapi sh

# Truy cập MySQL container
docker-compose exec mysql mysql -u root -p
```

### 3. Chạy Strapi commands

```bash
# Chạy migrations
docker-compose exec strapi yarn strapi migrate

# Tạo admin user
docker-compose exec strapi yarn strapi admin:create-user

# Build admin panel
docker-compose exec strapi yarn build
```

## Production Deployment

Để deploy production, sử dụng `docker-compose.prod.yml`:

1. Tạo file `.env.production` với cấu hình production
2. Cấu hình SSL certificates trong thư mục `nginx/ssl`
3. Chạy: `docker-compose -f docker-compose.prod.yml up -d`

## Monitoring

### Health Checks

Tất cả services đều có health checks:

```bash
# Kiểm tra trạng thái
docker-compose ps
```

### Logs

```bash
# Xem logs tất cả services
docker-compose logs

# Xem logs một service cụ thể
docker-compose logs strapi
docker-compose logs mysql

# Follow logs real-time
docker-compose logs -f strapi
```
