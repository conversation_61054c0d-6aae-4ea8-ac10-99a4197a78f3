# 🚀 Hướng dẫn khởi động nhanh Docker Compose

## Tình trạng hiện tại
Docker đang tải MySQL image (có thể mất 5-10 phút tùy tốc độ internet).

## Cách khởi động dự án

### 1. Khởi động đầy đủ (khuyến nghị)
```bash
# Sử dụng script quản lý
./docker-scripts.sh dev:start

# Hoặc sử dụng Docker Compose trực tiếp
docker-compose up -d
```

### 2. Khởi động chỉ cần thiết (nhanh hơn)
```bash
# Chỉ khởi động Strapi và MySQL (bỏ phpMyAdmin)
docker-compose up -d mysql strapi
```

### 3. Kiểm tra trạng thái
```bash
# Xem trạng thái containers
docker-compose ps

# Xem logs
docker-compose logs -f strapi
```

## Các URL sau khi khởi động thành công

- **Strapi Admin Panel**: http://localhost:1337/admin
- **Strapi API**: http://localhost:1337/api
- **phpMyAdmin** (nếu khởi động): http://localhost:8080

## Lệnh hữu ích

```bash
# Dừng tất cả services
./docker-scripts.sh dev:stop

# Xem logs real-time
./docker-scripts.sh dev:logs

# Backup database
./docker-scripts.sh db:backup

# Truy cập shell container
./docker-scripts.sh shell strapi

# Rebuild nếu có lỗi
./docker-scripts.sh rebuild
```

## Troubleshooting

### Nếu gặp lỗi port đã được sử dụng:
```bash
# Kiểm tra port
lsof -i :1337
lsof -i :3306

# Thay đổi port trong .env
PORT=1338
DATABASE_PORT=3307
```

### Nếu MySQL không khởi động:
```bash
# Xem logs MySQL
docker-compose logs mysql

# Restart MySQL
docker-compose restart mysql
```

### Nếu Strapi không kết nối được database:
```bash
# Đảm bảo MySQL đã sẵn sàng
docker-compose logs mysql | grep "ready for connections"

# Restart Strapi
docker-compose restart strapi
```

## Lần đầu tiên sử dụng

1. **Đợi MySQL tải xong** (có thể mất 5-10 phút)
2. **Khởi động services**: `./docker-scripts.sh dev:start`
3. **Truy cập**: http://localhost:1337/admin
4. **Tạo tài khoản admin** đầu tiên
5. **Bắt đầu sử dụng Strapi!**

## Cấu trúc dự án

```
trang_backend/
├── Dockerfile              # Container cho Strapi
├── docker-compose.yml      # Development environment
├── docker-compose.prod.yml # Production environment
├── docker-scripts.sh       # Script quản lý tiện lợi
├── .env                    # Biến môi trường
├── .dockerignore           # Loại trừ files khi build
├── healthcheck.js          # Health check cho container
├── nginx/                  # Cấu hình Nginx cho production
└── README.Docker.md        # Hướng dẫn chi tiết
```

## Ghi chú quan trọng

- **Node.js version**: 20.9.0 (như yêu cầu)
- **Database**: MySQL 8.0
- **Development mode**: Hot reload được bật
- **Production mode**: Sử dụng `docker-compose.prod.yml`
- **Data persistence**: Tất cả dữ liệu được lưu trong Docker volumes

---

**Tip**: Lần đầu tiên sẽ mất thời gian tải images. Những lần sau sẽ nhanh hơn nhiều!
