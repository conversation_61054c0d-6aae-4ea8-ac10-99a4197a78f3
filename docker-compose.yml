version: '3.8'

services:
  # Strapi Application
  strapi:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: trang-backend-strapi
    restart: unless-stopped
    env_file: .env
    environment:
      DATABASE_CLIENT: mysql
      DATABASE_HOST: host.docker.internal
      DATABASE_PORT: 3306
      DATABASE_NAME: ${DATABASE_NAME:-trang_db}
      DATABASE_USERNAME: ${DATABASE_USERNAME:-root}
      DATABASE_PASSWORD: ${DATABASE_PASSWORD:-root}
      NODE_ENV: ${NODE_ENV:-development}
    volumes:
      - ./config:/app/config
      - ./src:/app/src
      - ./package.json:/app/package.json
      - ./yarn.lock:/app/yarn.lock
      - strapi_uploads:/app/public/uploads
      - strapi_node_modules:/app/node_modules
    ports:
      - "${PORT:-1337}:1337"
    extra_hosts:
      - "host.docker.internal:host-gateway"
    command: ["yarn", "develop"]

volumes:
  strapi_uploads:
    driver: local
  strapi_node_modules:
    driver: local
