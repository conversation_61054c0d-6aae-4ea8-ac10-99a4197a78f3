version: '3.8'

services:
  # Strapi Application
  strapi:
    build: 
      context: .
      dockerfile: Dockerfile
    container_name: trang-backend-strapi
    restart: unless-stopped
    env_file: .env
    environment:
      DATABASE_CLIENT: mysql
      DATABASE_HOST: mysql
      DATABASE_PORT: 3306
      DATABASE_NAME: ${DATABASE_NAME:-trang_db}
      DATABASE_USERNAME: ${DATABASE_USERNAME:-strapi}
      DATABASE_PASSWORD: ${DATABASE_PASSWORD:-strapi}
      NODE_ENV: ${NODE_ENV:-development}
    volumes:
      - ./config:/app/config
      - ./src:/app/src
      - ./package.json:/app/package.json
      - ./yarn.lock:/app/yarn.lock
      - strapi_uploads:/app/public/uploads
      - strapi_node_modules:/app/node_modules
    ports:
      - "${PORT:-1337}:1337"
    networks:
      - strapi-network
    depends_on:
      mysql:
        condition: service_healthy
    command: ["yarn", "develop"]

  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: trang-backend-mysql
    restart: unless-stopped
    env_file: .env
    environment:
      MYSQL_DATABASE: ${DATABASE_NAME:-trang_db}
      MYSQL_USER: ${DATABASE_USERNAME:-strapi}
      MYSQL_PASSWORD: ${DATABASE_PASSWORD:-strapi}
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-root}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "${DATABASE_PORT:-3306}:3306"
    networks:
      - strapi-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # phpMyAdmin (optional - for database management)
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: trang-backend-phpmyadmin
    restart: unless-stopped
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: ${DATABASE_USERNAME:-strapi}
      PMA_PASSWORD: ${DATABASE_PASSWORD:-strapi}
    ports:
      - "${PHPMYADMIN_PORT:-8080}:80"
    networks:
      - strapi-network
    depends_on:
      mysql:
        condition: service_healthy

volumes:
  mysql_data:
    driver: local
  strapi_uploads:
    driver: local
  strapi_node_modules:
    driver: local

networks:
  strapi-network:
    driver: bridge
