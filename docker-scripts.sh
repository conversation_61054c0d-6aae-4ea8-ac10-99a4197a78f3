#!/bin/bash

# Docker management script for Trang Backend
# Usage: ./docker-scripts.sh [command]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

# Check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
}

# Development commands
dev_start() {
    print_header "Starting Development Environment"
    check_docker
    docker-compose up -d
    print_status "Development environment started!"
    print_status "Strapi: http://localhost:1337"
    print_status "phpMyAdmin: http://localhost:8080"
}

dev_stop() {
    print_header "Stopping Development Environment"
    docker-compose down
    print_status "Development environment stopped!"
}

dev_restart() {
    print_header "Restarting Development Environment"
    docker-compose restart
    print_status "Development environment restarted!"
}

dev_logs() {
    print_header "Showing Development Logs"
    docker-compose logs -f
}

# Production commands
prod_start() {
    print_header "Starting Production Environment"
    check_docker
    docker-compose -f docker-compose.prod.yml up -d
    print_status "Production environment started!"
}

prod_stop() {
    print_header "Stopping Production Environment"
    docker-compose -f docker-compose.prod.yml down
    print_status "Production environment stopped!"
}

# Database commands
db_backup() {
    print_header "Creating Database Backup"
    BACKUP_FILE="backup_$(date +%Y%m%d_%H%M%S).sql"
    docker-compose exec -T mysql mysqldump -u root -proot trang_db > "$BACKUP_FILE"
    print_status "Database backup created: $BACKUP_FILE"
}

db_restore() {
    if [ -z "$1" ]; then
        print_error "Please provide backup file: ./docker-scripts.sh db:restore backup.sql"
        exit 1
    fi
    
    if [ ! -f "$1" ]; then
        print_error "Backup file not found: $1"
        exit 1
    fi
    
    print_header "Restoring Database from $1"
    print_warning "This will overwrite existing data!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker-compose exec -T mysql mysql -u root -proot trang_db < "$1"
        print_status "Database restored from $1"
    else
        print_status "Database restore cancelled"
    fi
}

# Utility commands
clean() {
    print_header "Cleaning Docker Resources"
    print_warning "This will remove all containers, volumes, and images!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker-compose down -v --rmi all
        docker system prune -f
        print_status "Docker resources cleaned!"
    else
        print_status "Clean cancelled"
    fi
}

rebuild() {
    print_header "Rebuilding Application"
    docker-compose build --no-cache
    docker-compose up -d
    print_status "Application rebuilt and started!"
}

status() {
    print_header "Docker Services Status"
    docker-compose ps
}

shell() {
    SERVICE=${1:-strapi}
    print_header "Opening shell in $SERVICE container"
    docker-compose exec "$SERVICE" sh
}

# Show help
show_help() {
    echo "Docker Management Script for Trang Backend"
    echo ""
    echo "Usage: ./docker-scripts.sh [command]"
    echo ""
    echo "Development Commands:"
    echo "  dev:start     Start development environment"
    echo "  dev:stop      Stop development environment"
    echo "  dev:restart   Restart development environment"
    echo "  dev:logs      Show development logs"
    echo ""
    echo "Production Commands:"
    echo "  prod:start    Start production environment"
    echo "  prod:stop     Stop production environment"
    echo ""
    echo "Database Commands:"
    echo "  db:backup     Create database backup"
    echo "  db:restore    Restore database from backup file"
    echo ""
    echo "Utility Commands:"
    echo "  status        Show services status"
    echo "  rebuild       Rebuild and restart application"
    echo "  clean         Clean all Docker resources"
    echo "  shell [service] Open shell in container (default: strapi)"
    echo "  help          Show this help message"
    echo ""
    echo "Examples:"
    echo "  ./docker-scripts.sh dev:start"
    echo "  ./docker-scripts.sh db:backup"
    echo "  ./docker-scripts.sh db:restore backup_20231201_120000.sql"
    echo "  ./docker-scripts.sh shell mysql"
}

# Main command handler
case "$1" in
    "dev:start")
        dev_start
        ;;
    "dev:stop")
        dev_stop
        ;;
    "dev:restart")
        dev_restart
        ;;
    "dev:logs")
        dev_logs
        ;;
    "prod:start")
        prod_start
        ;;
    "prod:stop")
        prod_stop
        ;;
    "db:backup")
        db_backup
        ;;
    "db:restore")
        db_restore "$2"
        ;;
    "status")
        status
        ;;
    "rebuild")
        rebuild
        ;;
    "clean")
        clean
        ;;
    "shell")
        shell "$2"
        ;;
    "help"|"--help"|"-h")
        show_help
        ;;
    "")
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
